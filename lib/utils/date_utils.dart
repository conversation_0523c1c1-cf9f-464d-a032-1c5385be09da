import 'package:photo_manager/photo_manager.dart';

String formatDate(DateTime date) {
  if (date == DateTime(0)) return 'Unknown date';
  return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
}

DateTime getImageDate(AssetEntity image) {
  if (image.title != null) {
    final match = RegExp(r'IMG-(\d{8})').firstMatch(image.title!);
    if (match != null) {
      final dateStr = match.group(1)!;
      final year = int.parse(dateStr.substring(0, 4));
      final month = int.parse(dateStr.substring(4, 6));
      final day = int.parse(dateStr.substring(6, 8));
      return DateTime(year, month, day);
    }
  }
  return image.modifiedDateTime;
}

double easeInQuad(double x) {
  return x * x;
}
