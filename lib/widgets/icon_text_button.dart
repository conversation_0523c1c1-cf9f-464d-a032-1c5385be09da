import 'package:flutter/material.dart';

class IconTextButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback? onPressed;

  const IconTextButton({
    super.key,
    required this.icon,
    required this.label,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: Icon(
            icon,
            color: onPressed == null ? Colors.grey : Colors.white,
          ),
          onPressed: onPressed,
          iconSize: 32,
        ),
        Text(
          label,
          style: TextStyle(
            color: onPressed == null ? Colors.grey : Colors.white,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
