import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';
import 'package:image_swiper/models/deleted_image.dart';
import 'package:image_swiper/utils/date_utils.dart';
import 'package:image_swiper/widgets/icon_text_button.dart';

// Custom animation curve for a more pronounced bounce effect
class CustomBounceCurve extends Curve {
  final double strength;

  const CustomBounceCurve({this.strength = 0.3});

  @override
  double transformInternal(double t) {
    // Ease out
    t = 1.0 - t;
    // Apply bounce effect
    return 1.0 -
        (math.pow(t, 2) * math.cos(t * 6 * strength * math.pi) * strength);
  }
}

class ImageGalleryScreen extends StatefulWidget {
  const ImageGalleryScreen({super.key});

  @override
  ImageGalleryScreenState createState() => ImageGalleryScreenState();
}

class ImageGalleryScreenState extends State<ImageGalleryScreen>
    with TickerProviderStateMixin {
  List<AssetEntity> _images = [];
  List<int> _imageIndexes = [];
  List<DeletedImage> _deletedImages = [];
  int _currentIndex = 0;
  final Map<int, Uint8List?> _imageCache = {};
  Uint8List? _currentImage;
  double _dragDistance = 0.0;
  bool _isDragging = false;
  Offset _dragOffset = Offset.zero;
  bool _isDraggingFree = false;
  bool _isReturningFromDrag = false;
  Offset _initialDragPosition = Offset.zero;
  double _maxPossibleDragDistance = 0.0;
  Offset _trashIconPosition = Offset.zero;
  Offset _deleteReleaseVisualPosition = Offset.zero;
  double _deleteReleaseScale = 1.0;
  double _deleteReleaseRotation = 0.0;
  late AnimationController _deleteAnimController;
  late AnimationController _dragAnimController;
  late AnimationController _panAnimController;
  late Animation<double> _dragAnimation;
  late Animation<Offset> _panAnimation;
  bool _isDeleting = false;

  @override
  void initState() {
    super.initState();
    _loadImages();

    // Initialize animation controllers with longer duration for smoother animations
    _deleteAnimController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _dragAnimController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _panAnimController = AnimationController(
      duration: const Duration(
        milliseconds: 800,
      ), // Longer duration for elastic effect
      vsync: this,
    );

    // Initialize animations with default values
    _dragAnimation = Tween<double>(begin: 0.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _dragAnimController,
        curve: Curves.easeOutQuart, // Use a stronger ease out curve
      ),
    );

    _panAnimation = Tween<Offset>(begin: Offset.zero, end: Offset.zero).animate(
      CurvedAnimation(
        parent: _panAnimController,
        curve: const CustomBounceCurve(
          strength: 0.5,
        ), // Use our custom bounce curve for consistency
      ),
    );

    // Add listeners to update the UI when animations change
    _dragAnimController.addListener(() {
      if (mounted) {
        setState(() {
          _dragDistance = _dragAnimation.value;
        });
      }
    });

    _panAnimController.addListener(() {
      if (mounted) {
        setState(() {
          _dragOffset = _panAnimation.value;
        });
      }
    });
  }

  @override
  void dispose() {
    _deleteAnimController.dispose();
    _dragAnimController.dispose();
    _panAnimController.dispose();
    super.dispose();
  }

  Future<void> _loadImages() async {
    final PermissionState ps = await PhotoManager.requestPermissionExtend();
    if (ps.isAuth) {
      final List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
        onlyAll: true,
        type: RequestType.image,
      );

      if (albums.isNotEmpty) {
        final AssetPathEntity album = albums[0];
        final List<AssetEntity> photos = await album.getAssetListPaged(
          page: 0,
          size: 10000,
        );

        setState(() {
          _images = photos.reversed.toList();
          _imageIndexes = List.generate(_images.length, (index) => index);
        });
        _loadCurrentImage();
      }
    } else {
      print('Permission denied');
    }
  }

  Future<void> _loadCurrentImage() async {
    if (_imageIndexes.isEmpty) {
      return;
    }
    // With AssetEntityImage, we don't need to manually load image data
    // The widget handles loading internally
    if (mounted) {
      setState(() {
        // Just trigger a rebuild - the AssetEntityImage will handle loading
      });
    }
  }

  Future<Uint8List?> _getCachedOrFetchImageData(int index) async {
    if (_imageCache.containsKey(index)) {
      return _imageCache[index];
    }
    final data = await _fetchImageData(_images[index]);
    _imageCache[index] = data;
    return data;
  }

  Future<Uint8List?> _fetchImageData(AssetEntity asset) async {
    try {
      final data = await asset.thumbnailDataWithSize(
        const ThumbnailSize(800, 800),
        quality: 95,
      );
      return data;
    } catch (e) {
      print('Error fetching image: $e');
      return null;
    }
  }

  void _onHorizontalDragStart(DragStartDetails details) {
    if (_imageIndexes.isEmpty || _isDeleting || _isDraggingFree) return;
    setState(() {
      _isDragging = true;
      _dragDistance = 0;
    });
  }

  void _onHorizontalDragUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;
    setState(() {
      _dragDistance += details.delta.dx;
      if ((_currentIndex <= 0 && _dragDistance > 0) ||
          (_currentIndex >= _imageIndexes.length - 1 && _dragDistance < 0)) {
        _dragDistance = _dragDistance * 0.4;
      }
    });
  }

  void _onHorizontalDragEnd(DragEndDetails details) {
    if (!_isDragging) return;
    final screenWidth = MediaQuery.of(context).size.width;
    final dragPercentage = _dragDistance.abs() / screenWidth;
    final velocity = details.primaryVelocity ?? 0;
    bool shouldSwitchImage = false;
    bool isMovingForward = false;
    if (_dragDistance < 0 && _currentIndex < _imageIndexes.length - 1) {
      isMovingForward = true;
      shouldSwitchImage = dragPercentage > 0.2 || velocity < -300;
    } else if (_dragDistance > 0 && _currentIndex > 0) {
      isMovingForward = false;
      shouldSwitchImage = dragPercentage > 0.2 || velocity > 300;
    }

    if (shouldSwitchImage) {
      setState(() {
        _dragDistance = isMovingForward ? -screenWidth : screenWidth;
      });
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          setState(() {
            _currentIndex += isMovingForward ? 1 : -1;
            _dragDistance = 0;
            _isDragging = false;
            _loadCurrentImage();
          });
        }
      });
    } else {
      // Use animation controller for smooth animation
      _dragAnimController.reset();

      // Set up the animation with a stronger ease out curve for smoother return
      _dragAnimation = Tween<double>(begin: _dragDistance, end: 0.0).animate(
        CurvedAnimation(
          parent: _dragAnimController,
          curve:
              Curves.easeOutQuart, // Stronger ease out for smoother animation
        ),
      );

      // When animation completes, reset dragging state
      _dragAnimController.addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            _isDragging = false;
          });
          // Remove the listener to avoid memory leaks
          _dragAnimController.removeStatusListener((status) {});
        }
      });

      // Start the animation from the beginning
      _dragAnimController.forward(from: 0.0);
    }
  }

  void _onPanStart(DragStartDetails details) {
    if (_imageIndexes.isEmpty || _isDeleting || _isDragging) return;
    final screenWidth = MediaQuery.of(context).size.width;
    _initialDragPosition = details.globalPosition;
    _trashIconPosition = Offset(screenWidth - 30, 50);
    _maxPossibleDragDistance =
        (_initialDragPosition - _trashIconPosition).distance;
    _maxPossibleDragDistance = _maxPossibleDragDistance.clamp(
      200.0,
      double.infinity,
    );
    if (_currentIndex < _imageIndexes.length - 1) {
      _getCachedOrFetchImageData(_imageIndexes[_currentIndex + 1]);
    }
    setState(() {
      _isDraggingFree = true;
      _dragOffset = Offset.zero;
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDraggingFree) return;
    setState(() {
      _dragOffset = Offset(
        _dragOffset.dx + details.delta.dx,
        _dragOffset.dy + details.delta.dy,
      );
    });
  }

  void _onPanEnd(DragEndDetails details) {
    if (!_isDraggingFree) return;
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final counterAreaHeight = 80.0;
    final progressTowardTrash = _getProgressTowardTrash();
    final wasSwipedTowardTrash =
        _dragOffset.dy < 0 && progressTowardTrash >= 0.3;
    final hasUpwardVelocity = details.velocity.pixelsPerSecond.dy < -300;

    if (wasSwipedTowardTrash || hasUpwardVelocity) {
      // Only apply rotation and scaling for the delete animation
      final imageCenter = Offset(
        screenWidth / 2 + _dragOffset.dx,
        (screenHeight - counterAreaHeight) / 2 +
            counterAreaHeight +
            _dragOffset.dy,
      );
      final releaseScale =
          _dragOffset.dy < 0
              ? (1.0 - 0.1 - ((_getProgressTowardTrash() * 0.6))).clamp(
                0.3,
                0.9,
              )
              : 1.0;
      final releaseRotation =
          _dragOffset.dy < 0
              ? (_getProgressTowardTrash() * 0.7 * (3.14159 / 18)).clamp(
                0.0,
                3.14159 / 12,
              )
              : 0.0;
      final trashPosition = Offset(screenWidth - 20, 20);

      if (_currentIndex < _imageIndexes.length - 1) {
        final nextImageIndex = _imageIndexes[_currentIndex + 1];
        _getCachedOrFetchImageData(nextImageIndex);
      }

      setState(() {
        _isDeleting = true;
        _dragOffset = _dragOffset;
        _deleteReleaseVisualPosition = imageCenter;
        _deleteReleaseScale = releaseScale;
        _deleteReleaseRotation = releaseRotation;
        _trashIconPosition = trashPosition;
      });

      Future.delayed(const Duration(milliseconds: 400), () {
        if (mounted) {
          setState(() {
            _deletedImages.add(
              DeletedImage(
                index: _imageIndexes[_currentIndex],
                originalPosition: _currentIndex,
              ),
            );
            _imageIndexes.removeAt(_currentIndex);
            if (_imageIndexes.isEmpty) {
              _currentIndex = 0;
            } else if (_currentIndex >= _imageIndexes.length) {
              _currentIndex = _imageIndexes.length - 1;
            }
            _isDraggingFree = false;
            _isDeleting = false;
            _isReturningFromDrag = false;
            _dragOffset = Offset.zero;
            _deleteReleaseVisualPosition = Offset.zero;
            _deleteReleaseScale = 1.0;
            _deleteReleaseRotation = 0.0;
            _loadCurrentImage();
          });
        }
      });
    } else {
      setState(() {
        _isReturningFromDrag = true;
      });

      // Use a delayed setState to reset the states after animation completes
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          setState(() {
            _isDraggingFree = false;
            _isReturningFromDrag = false;
            _dragOffset = Offset.zero;
          });
        }
      });
    }
  }

  void _deleteImage() {
    if (_imageIndexes.isEmpty || _isDeleting) return;
    setState(() {
      _deletedImages.add(
        DeletedImage(
          index: _imageIndexes[_currentIndex],
          originalPosition: _currentIndex,
        ),
      );
      _imageIndexes.removeAt(_currentIndex);
      if (_imageIndexes.isEmpty) {
        _currentIndex = 0;
      } else if (_currentIndex >= _imageIndexes.length) {
        _currentIndex = _imageIndexes.length - 1;
      }
      _loadCurrentImage();
    });
  }

  void _undoDeleteImage() {
    if (_deletedImages.isEmpty || _isDeleting) return;
    final lastDeleted = _deletedImages.removeLast();
    final int originalIndex = lastDeleted.index;
    int targetPosition = lastDeleted.originalPosition;
    if (targetPosition > _imageIndexes.length) {
      targetPosition = _imageIndexes.length;
    }
    setState(() {
      _imageIndexes.insert(targetPosition, originalIndex);
      _currentIndex = targetPosition;
      _loadCurrentImage();
    });
  }

  void _undoAction() {
    if (_deletedImages.isNotEmpty) {
      _undoDeleteImage();
    } else if (_currentIndex > 0) {
      setState(() {
        _isDragging = true;
        _dragDistance = MediaQuery.of(context).size.width;
      });
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          setState(() {
            _currentIndex--;
            _dragDistance = 0;
          });
          _loadCurrentImage().then((_) {
            if (mounted) {
              setState(() {
                _isDragging = false;
              });
            }
          });
        }
      });
    }
  }

  Future<void> _clearTrash() async {
    if (_deletedImages.isEmpty) return;
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Clear Trash'),
          content: Text(
            'Are you sure you want to permanently delete ${_deletedImages.length} image${_deletedImages.length == 1 ? '' : 's'}?',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      try {
        final List<String> idsToDelete =
            _deletedImages.map((deleted) => _images[deleted.index].id).toList();
        await PhotoManager.editor.deleteWithIds(idsToDelete);
        for (final deleted in _deletedImages) {
          _imageCache.remove(deleted.index);
        }
        setState(() {
          _deletedImages.clear();
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Images permanently deleted'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        print('Error deleting images: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to delete images'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _goToNextImage() {
    if (!_canGoNext()) return;
    final screenWidth = MediaQuery.of(context).size.width;
    setState(() {
      _isDragging = true;
      _dragDistance = -screenWidth;
    });
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          _currentIndex++;
          _dragDistance = 0;
        });
        _loadCurrentImage().then((_) {
          if (mounted) {
            setState(() {
              _isDragging = false;
            });
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final statusBarHeight = MediaQuery.of(context).padding.top;

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          Column(
            children: [
              SizedBox(height: statusBarHeight),

              SizedBox(
                height: 56,
                child: Stack(
                  children: [
                    Positioned(
                      top: 0,
                      right: 16,
                      child: Container(
                        width: 56,
                        height: 56,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            IconButton(
                              icon: const Icon(
                                Icons.delete_outline,
                                color: Colors.white,
                                size: 28,
                              ),
                              onPressed:
                                  _deletedImages.isEmpty ? null : _clearTrash,
                              padding: const EdgeInsets.all(12.0),
                              constraints: const BoxConstraints(
                                minWidth: 56,
                                minHeight: 56,
                              ),
                            ),
                            if (_deletedImages.isNotEmpty)
                              Positioned(
                                right: 4,
                                top: 4,
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  constraints: const BoxConstraints(
                                    minWidth: 16,
                                    minHeight: 16,
                                  ),
                                  child: Text(
                                    _deletedImages.length.toString(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              if (_imageIndexes.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Column(
                    children: [
                      Text(
                        'Image ${_currentIndex + 1} of ${_imageIndexes.length}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Taken on ${formatDate(getImageDate(_getCurrentImageAsset()))}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

              Expanded(
                child:
                    _imageIndexes.isEmpty
                        ? const Center(
                          child: Text(
                            'No images found',
                            style: TextStyle(color: Colors.white),
                          ),
                        )
                        : _buildImageViewerContent(),
              ),

              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 24.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      IconTextButton(
                        icon: Icons.arrow_forward,
                        label: 'Next',
                        onPressed: _canGoNext() ? () => _goToNextImage() : null,
                      ),
                      IconTextButton(
                        icon: Icons.undo,
                        label: 'Undo',
                        onPressed: _canUndo() ? _undoAction : null,
                      ),
                      IconTextButton(
                        icon: Icons.delete,
                        label: 'Delete',
                        onPressed: _imageIndexes.isEmpty ? null : _deleteImage,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          _buildIndicatorsLayer(),
        ],
      ),
    );
  }

  Widget _buildIndicatorsLayer() {
    return Positioned.fill(
      child: Stack(
        children: [
          if (_isDragging) ..._buildSwipeIndicators(),
          if (_isDraggingFree && !_isDeleting) _buildDeleteIndicator(),
        ],
      ),
    );
  }

  Widget _buildImageViewerContent() {
    if (_imageIndexes.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    // Test with both approaches
    final currentAsset = _images[_imageIndexes[_currentIndex]];
    return Container(
      color: Colors.black,
      child: Column(
        children: [
          // Top: Network image test
          Expanded(
            child: Container(
              color: Colors.grey.withOpacity(0.3),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Network Image Test:',
                      style: TextStyle(color: Colors.white),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      width: 150,
                      height: 150,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.yellow, width: 3),
                        color: Colors.blue,
                      ),
                      child: Image.network(
                        'https://picsum.photos/200/200',
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.red,
                            child: const Center(
                              child: Text(
                                'NET ERROR',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          );
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return const Center(
                            child: CircularProgressIndicator(
                              color: Colors.white,
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Middle: AssetEntityImage
          Expanded(
            child: Container(
              color: Colors.grey.withOpacity(0.2),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'AssetEntityImage:',
                      style: TextStyle(color: Colors.white),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      width: 150,
                      height: 150,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.green, width: 3),
                        color: Colors.purple,
                      ),
                      child: AssetEntityImage(
                        currentAsset,
                        isOriginal: false,
                        thumbnailSize: const ThumbnailSize.square(400),
                        thumbnailFormat: ThumbnailFormat.png,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          print('AssetEntityImage error: $error');
                          return Container(
                            color: Colors.red,
                            child: const Center(
                              child: Text(
                                'ASSET ERROR',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Bottom: Image.memory
          Expanded(
            child: Container(
              color: Colors.grey.withOpacity(0.1),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Image.memory:',
                      style: TextStyle(color: Colors.white),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      width: 150,
                      height: 150,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.orange, width: 3),
                        color: Colors.cyan,
                      ),
                      child: FutureBuilder<Uint8List?>(
                        future: currentAsset.thumbnailDataWithSize(
                          const ThumbnailSize.square(400),
                          quality: 95,
                        ),
                        builder: (context, snapshot) {
                          if (snapshot.hasError) {
                            print('Image.memory error: ${snapshot.error}');
                            return Container(
                              color: Colors.red,
                              child: const Center(
                                child: Text(
                                  'MEM ERROR',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            );
                          }
                          if (snapshot.hasData && snapshot.data != null) {
                            print(
                              'Image.memory loaded: ${snapshot.data!.length} bytes',
                            );
                            return Image.memory(
                              snapshot.data!,
                              fit: BoxFit.contain,
                            );
                          }
                          return Container(
                            color: Colors.blue,
                            child: const Center(
                              child: CircularProgressIndicator(
                                color: Colors.white,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDraggingImageLayer() {
    if (_imageIndexes.isEmpty) return Container();
    final currentAsset = _images[_imageIndexes[_currentIndex]];

    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    if (_isDeleting) {
      return _buildDeleteAnimation();
    } else if (_isReturningFromDrag) {
      // Use a similar animation to the delete animation, but animate back to center
      return TweenAnimationBuilder<double>(
        tween: Tween<double>(begin: 0.0, end: 1.0),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
        builder: (context, progress, child) {
          // Calculate the current position based on progress
          final currentOffset = Offset(
            _dragOffset.dx * (1 - progress),
            _dragOffset.dy * (1 - progress),
          );

          return Positioned.fill(
            child: Transform.translate(
              offset: currentOffset,
              child: _buildAssetImageWidget(currentAsset),
            ),
          );
        },
      );
    } else {
      // During active dragging, just use Transform.translate
      return Positioned.fill(
        child: Transform.translate(
          offset: _dragOffset,
          child: _buildAssetImageWidget(currentAsset),
        ),
      );
    }
  }

  Widget _buildCurrentImage() {
    if (_imageIndexes.isEmpty) return Container();
    final currentAsset = _images[_imageIndexes[_currentIndex]];

    if (_isDragging) {
      return Positioned.fill(
        child: Transform.translate(
          offset: Offset(_dragDistance, 0),
          child: _buildAssetImageWidget(currentAsset),
        ),
      );
    } else {
      return Positioned.fill(child: _buildAssetImageWidget(currentAsset));
    }
  }

  Widget _buildDeleteAnimation() {
    final startPos = _deleteReleaseVisualPosition;
    final endPos = _trashIconPosition;

    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeOut,
      builder: (context, progress, child) {
        final double x = startPos.dx + (endPos.dx - startPos.dx) * progress;
        final double y = startPos.dy + (endPos.dy - startPos.dy) * progress;
        final currentPos = Offset(x, y);
        final scale =
            _deleteReleaseScale * (1.0 - progress * 0.9).clamp(0.1, 1.0);

        // Calculate the offset relative to the center of the image viewing area
        final viewerWidth = MediaQuery.of(context).size.width;
        final viewerHeight = MediaQuery.of(context).size.height;

        // Calculate the center of the image viewing area
        final centerX = viewerWidth / 2;
        final centerY = viewerHeight / 2;

        // Calculate the offset from center
        final offsetX = currentPos.dx - centerX;
        final offsetY = currentPos.dy - centerY;

        return Opacity(
          opacity: (1.0 - progress * 0.8).clamp(0.2, 1.0),
          child: Transform.translate(
            offset: Offset(offsetX, offsetY),
            child: Transform.scale(
              scale: scale,
              child: Transform.rotate(
                angle: _deleteReleaseRotation,
                child: _buildImageWidget(_currentImage!),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBackgroundImage() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final counterAreaHeight = 80.0;
    final imageAreaHeight = screenHeight - counterAreaHeight;

    if (_isDragging) {
      double dragProgress = (_dragDistance.abs() / screenWidth).clamp(0.0, 1.0);

      if (_dragDistance > 0 && _currentIndex > 0) {
        final prevImageIndex = _imageIndexes[_currentIndex - 1];
        final prevImageData = _imageCache[prevImageIndex];
        if (prevImageData != null) {
          return Positioned.fill(
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: dragProgress),
              duration: const Duration(milliseconds: 100),
              curve: Curves.easeOut,
              builder: (context, animatedProgress, child) {
                return Opacity(
                  opacity: animatedProgress,
                  child: Transform.scale(
                    scale: 0.3 + (animatedProgress * 0.7),
                    alignment: Alignment.center,
                    child: _buildImageWidget(prevImageData),
                  ),
                );
              },
            ),
          );
        }
      } else if (_dragDistance < 0 &&
          _currentIndex < _imageIndexes.length - 1) {
        final nextImageIndex = _imageIndexes[_currentIndex + 1];
        final nextImageData = _imageCache[nextImageIndex];
        if (nextImageData != null) {
          return Positioned.fill(
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: dragProgress),
              duration: const Duration(milliseconds: 100),
              curve: Curves.easeOut,
              builder: (context, animatedProgress, child) {
                return Opacity(
                  opacity: animatedProgress,
                  child: Transform.scale(
                    scale: 0.3 + (animatedProgress * 0.7),
                    alignment: Alignment.center,
                    child: _buildImageWidget(nextImageData),
                  ),
                );
              },
            ),
          );
        }
      }
    } else if ((_isDraggingFree && _dragOffset.dy < 0) || _isDeleting) {
      if (_currentIndex < _imageIndexes.length - 1) {
        final nextImageIndex = _imageIndexes[_currentIndex + 1];
        final nextImageData = _imageCache[nextImageIndex];
        if (nextImageData != null) {
          double dragProgress =
              _isDeleting ? 1.0 : _getProgressTowardTrash().clamp(0.0, 1.0);

          return Positioned.fill(
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: dragProgress),
              duration: Duration(milliseconds: _isDeleting ? 800 : 100),
              curve: Curves.easeOut,
              builder: (context, animatedProgress, child) {
                return Opacity(
                  opacity: animatedProgress,
                  child: Transform.scale(
                    scale: 0.3 + (animatedProgress * 0.7),
                    alignment: Alignment.center,
                    child: _buildImageWidget(nextImageData),
                  ),
                );
              },
            ),
          );
        }
      } else if (_imageIndexes.length > 1) {
        final prevImageIndex = _imageIndexes[_currentIndex - 1];
        final prevImageData = _imageCache[prevImageIndex];
        if (prevImageData != null) {
          return Positioned.fill(
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: _isDeleting ? 1.0 : 0.0),
              duration: Duration(milliseconds: _isDeleting ? 800 : 100),
              curve: Curves.easeOut,
              builder: (context, animatedProgress, child) {
                return Opacity(
                  opacity: animatedProgress,
                  child: Transform.scale(
                    scale: 0.3 + (animatedProgress * 0.7),
                    alignment: Alignment.center,
                    child: _buildImageWidget(prevImageData),
                  ),
                );
              },
            ),
          );
        }
      }
    }

    return Container(color: Colors.transparent);
  }

  Widget _buildAnimatedBackgroundImage(
    double progress,
    Uint8List imageData,
    double counterAreaHeight,
    double imageAreaHeight,
    double screenWidth, {
    int duration = 100,
  }) {
    return Positioned.fill(
      top: counterAreaHeight,
      bottom: 0,
      child: TweenAnimationBuilder<double>(
        tween: Tween<double>(begin: 0.0, end: progress),
        duration: Duration(milliseconds: duration),
        curve: Curves.easeOut,
        builder: (context, animatedProgress, child) {
          return Center(
            child: Opacity(
              opacity: animatedProgress,
              child: Transform.scale(
                scale: 0.3 + (animatedProgress * 0.7),
                child: _buildImageWidget(imageData),
              ),
            ),
          );
        },
      ),
    );
  }

  List<Widget> _buildSwipeIndicators() {
    final screenWidth = MediaQuery.of(context).size.width;
    final dragPercentage = (_dragDistance / screenWidth).abs().clamp(0.0, 1.0);

    List<Widget> indicators = [];

    if (_dragDistance < 0 && _currentIndex < _imageIndexes.length - 1) {
      indicators.add(
        Positioned(
          right: 20,
          top: MediaQuery.of(context).size.height / 2 - 40,
          child: Icon(
            Icons.arrow_forward_ios,
            color: Colors.white.withOpacity(0.5 + dragPercentage * 0.5),
            size: 40 + dragPercentage * 20,
          ),
        ),
      );
    }

    if (_dragDistance > 0 && _currentIndex > 0) {
      indicators.add(
        Positioned(
          left: 20,
          top: MediaQuery.of(context).size.height / 2 - 40,
          child: Icon(
            Icons.arrow_back_ios,
            color: Colors.white.withOpacity(0.5 + dragPercentage * 0.5),
            size: 40 + dragPercentage * 20,
          ),
        ),
      );
    }

    return indicators;
  }

  Widget _buildDeleteIndicator() {
    return Positioned(
      top: 180,
      left: 0,
      right: 0,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.delete_outline,
              color: Colors.red.withOpacity(
                0.5 + ((_dragOffset.dy.abs() / 80) * 0.5).clamp(0.0, 0.5),
              ),
              size: 40 + ((_dragOffset.dy.abs() / 80) * 30).clamp(0.0, 30.0),
            ),
            const SizedBox(height: 8),
            Text(
              _getProgressTowardTrash() >= 0.25
                  ? "Release to Delete"
                  : "Drag Toward Trash to Delete",
              style: TextStyle(
                color: Colors.red.withOpacity(
                  0.5 + (_getProgressTowardTrash() * 0.5).clamp(0.0, 0.5),
                ),
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            if (_dragOffset.dy.abs() > 10)
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 100,
                height: 4,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  color: Colors.grey.withOpacity(0.3),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: (_getProgressTowardTrash() * 2).clamp(0.0, 1.0),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                      color:
                          _getProgressTowardTrash() >= 0.3
                              ? Colors.red
                              : Colors.orange,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageWidget(Uint8List imageData) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      alignment: Alignment.center,
      child: Image.memory(
        imageData,
        fit: BoxFit.contain,
        alignment: Alignment.center,
        gaplessPlayback: true,
        filterQuality: FilterQuality.high,
      ),
    );
  }

  Widget _buildAssetImageWidget(AssetEntity asset) {
    print('Building AssetEntityImage for asset: ${asset.id}');
    return Container(
      width: double.infinity,
      height: double.infinity,
      alignment: Alignment.center,
      child: AssetEntityImage(
        asset,
        isOriginal: false,
        thumbnailSize: const ThumbnailSize.square(800),
        thumbnailFormat: ThumbnailFormat.jpeg,
        fit: BoxFit.contain,
        filterQuality: FilterQuality.high,
        errorBuilder: (context, error, stackTrace) {
          print('Error loading image: $error');
          return Container(
            color: Colors.red,
            child: const Center(
              child: Text(
                'Error loading image',
                style: TextStyle(color: Colors.white),
              ),
            ),
          );
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) {
            print('Image loaded successfully');
            return child;
          }
          print(
            'Loading image: ${loadingProgress.cumulativeBytesLoaded}/${loadingProgress.expectedTotalBytes}',
          );
          return const Center(
            child: CircularProgressIndicator(color: Colors.white),
          );
        },
      ),
    );
  }

  bool _canGoNext() {
    return _imageIndexes.isNotEmpty &&
        !_isDeleting &&
        _currentIndex < _imageIndexes.length - 1;
  }

  bool _canUndo() {
    return (_deletedImages.isNotEmpty || _currentIndex > 0) && !_isDeleting;
  }

  AssetEntity _getCurrentImageAsset() {
    return _images[_imageIndexes[_currentIndex]];
  }

  double _getProgressTowardTrash() {
    if (_dragOffset.dy < 0) {
      return (_dragOffset.dy.abs() / _maxPossibleDragDistance).clamp(0.0, 1.0);
    }
    return 0.0;
  }

  double easeInQuad(double t) {
    return t * t;
  }
}
